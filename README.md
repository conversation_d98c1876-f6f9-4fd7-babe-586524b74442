# ECG_PLATFORM

ECG_PLATFORM is a comprehensive Python framework designed for testing QRS detectors and ECG delineators on publicly available datasets. This platform provides researchers and developers with a standardized environment for benchmarking ECG signal processing algorithms, enabling reproducible and comparable evaluation of cardiac signal analysis methods across multiple well-established medical databases.

## Background: How an ECG System Works

```
+-----------------+      +---------------------------+      +---------------------+      +---------------------+      +-----------------+      +---------------------------+      +----------------------+
| ECG Electrodes  |----->| Low-Noise Instrumentation |----->|   Filtering Stage   |----->| Amplification Stage |----->|  ADC Converter  |----->| Digital Signal Processing |----->|    Display/Analysis   |
+-----------------+      +---------------------------+      +---------------------+      +---------------------+      +-----------------+      +---------------------------+      +----------------------+
       |
       |
       V
+-----------------+
| Patient's Heart |
+-----------------+
```

### Explanation of Blocks

1. **ECG Electrodes**: Detect the small electrical signals generated by the heart. Placed on the patient's skin at specific locations.
2. **Low-Noise Instrumentation Amplifier**: Amplifies the weak ECG signal while minimizing noise. High common-mode rejection ratio (CMRR) is crucial.
3. **Filtering Stage**: Removes unwanted noise and artifacts from the signal. Includes filters like band-pass, notch, high-pass, and low-pass.
4. **Amplification Stage**: Further amplifies the filtered ECG signal to a suitable level for the ADC. Variable gain amplifiers may be used.
5. **ADC Converter**: Converts the analog ECG signal into a digital signal. The resolution and sampling rate of the ADC are important.
6. **Digital Signal Processing**: Processes the digital signal to extract relevant information. Includes tasks like noise reduction, feature extraction, and heart rate calculation.
7. **Display/Analysis**: Displays the ECG waveform for visualization and analysis. May include automated analysis for detecting abnormalities.

## Features

### Included Algorithms

1. **engzee segmenter** - André Lourenço, Hugo Silva, Paulo Leite, Renato Lourenço, and Ana Fred. Real Time Electrocardiogram Segmentation for Finger Based ECG Biometrics.

2. **hamilton segmenter** - Hamilton, Tompkins, W. J., "Quantitative investigation of QRS detection rules using the MIT/BIH arrhythmia database", IEEE Trans. Biomed. Eng., BME-33, pp. 1158-1165, 1987.

3. **WT delineator** - Juan Pablo Martínez, Rute Almeida, Salvador Olmos, Member, IEEE, Ana Paula Rocha, and Pablo Laguna, Member, IEEE A Wavelet-Based ECG Delineator: Evaluation on Standard Databases.

4. **ECGPUWAVE** - [https://www.physionet.org/content/ecgpuwave/1.3.4/](https://www.physionet.org/content/ecgpuwave/1.3.4/)

## Supported Datasets

1. **cinc1** - [https://physionet.org/pn3/challenge/2014/set-p/](https://physionet.org/pn3/challenge/2014/set-p/)
2. **cinc2** - [https://physionet.org/pn3/challenge/2014/set-p2/](https://physionet.org/pn3/challenge/2014/set-p2/)
3. **mitdb** - [https://archive.physionet.org/physiobank/database/mitdb/](https://archive.physionet.org/physiobank/database/mitdb/)
4. **mitdb (pwave annotations)** - [https://archive.physionet.org/physiobank/database/pwave/](https://archive.physionet.org/physiobank/database/pwave/)
5. **qtdb** - [https://physionet.org/content/qtdb/1.0.0/](https://physionet.org/content/qtdb/1.0.0/)
6. **ludb** - [https://physionet.org/content/ludb/1.0.0/](https://physionet.org/content/ludb/1.0.0/)
7. **telehealth** - [https://dataverse.harvard.edu/dataset.xhtml?persistentId=doi:10.7910/DVN/QTG0EP](https://dataverse.harvard.edu/dataset.xhtml?persistentId=doi:10.7910/DVN/QTG0EP)
8. **BUT PDB** - [https://www.physionet.org/content/but-pdb/1.0.0/](https://www.physionet.org/content/but-pdb/1.0.0/)

## Requirements & Installation

### Dependencies

The following Python packages are required:

- biosppy
- wfdb
- numpy
- pandas
- h5py
- scipy
- matplotlib
- scikit-learn

### Installation

Install all required dependencies using pip:

```bash
pip install biosppy wfdb numpy pandas h5py scipy matplotlib scikit-learn
```

## Usage Examples

The platform includes several example scripts to demonstrate its capabilities:

- `Example_Delineation.py` - ECG delineation examples
- `Example_Ecgpuwave.py` - ECGPUWAVE algorithm demonstration
- `Examples5DBs_4algo.py` - Multi-database comparison with 4 algorithms
- `Framework/HowToExample.py` - Basic framework usage tutorial

## Project Structure

```
ECG_PLATFORM/
├── Algorithms_sandbox/     # Algorithm implementations and testing
├── Datasets/              # Dataset storage directory
├── Framework/             # Core testing framework
├── Helpers/               # Utility functions and ECG processing tools
├── Results/               # Analysis results and performance metrics
├── Example_*.py           # Usage examples
└── README.md
```
